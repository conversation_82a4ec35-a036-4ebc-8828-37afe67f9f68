/**
 * Storage Objects API Route
 * Manage objects within storage buckets
 */

import { NextRequest, NextResponse } from 'next/server';
import { storageService } from '@/lib/infrastructure/services/storage';
import { requireStorageAccess, createInfrastructureAuditLog } from '@/lib/auth/infrastructure-auth';
import { InfrastructureAuthError } from '@/lib/auth/infrastructure-auth';

interface RouteParams {
  params: {
    bucket: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication and permissions
    const session = await requireStorageAccess('read');
    const { bucket } = params;

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const prefix = searchParams.get('prefix') || '';
    const maxKeys = parseInt(searchParams.get('maxKeys') || '1000');
    const delimiter = searchParams.get('delimiter') || '';

    // List objects in bucket
    const result = await storageService.listObjects(bucket, prefix);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    // Filter and paginate results
    let objects = result.data || [];
    
    if (delimiter) {
      // Group objects by delimiter (e.g., show folders)
      const folders = new Set<string>();
      const files: any[] = [];

      objects.forEach(obj => {
        const relativePath = obj.key.substring(prefix.length);
        const delimiterIndex = relativePath.indexOf(delimiter);
        
        if (delimiterIndex > 0) {
          const folderName = relativePath.substring(0, delimiterIndex + 1);
          folders.add(prefix + folderName);
        } else if (relativePath && !relativePath.endsWith(delimiter)) {
          files.push(obj);
        }
      });

      objects = [
        ...Array.from(folders).map(folder => ({
          key: folder,
          size: 0,
          lastModified: new Date(),
          etag: '',
          contentType: 'application/x-directory',
          isFolder: true,
        })),
        ...files,
      ];
    }

    // Apply pagination
    objects = objects.slice(0, maxKeys);

    // Log access
    await createInfrastructureAuditLog(
      'storage.list_objects',
      'storage_bucket',
      bucket,
      { prefix, objectCount: objects.length },
      request
    );

    return NextResponse.json({
      success: true,
      bucket,
      prefix,
      objects,
      count: objects.length,
      truncated: objects.length === maxKeys,
    });

  } catch (error) {
    console.error('Failed to list storage objects:', error);

    if (error instanceof InfrastructureAuthError) {
      return NextResponse.json(
        { error: error.message },
        { status: error.code === 'INSUFFICIENT_PERMISSIONS' ? 403 : 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to list objects' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication and permissions
    const session = await requireStorageAccess('write');
    const { bucket } = params;

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const key = formData.get('key') as string;
    const metadata = formData.get('metadata') as string;

    if (!file || !key) {
      return NextResponse.json(
        { error: 'File and key are required' },
        { status: 400 }
      );
    }

    // Parse metadata if provided
    let parsedMetadata = {};
    if (metadata) {
      try {
        parsedMetadata = JSON.parse(metadata);
      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid metadata JSON' },
          { status: 400 }
        );
      }
    }

    // Upload object
    const result = await storageService.uploadObject({
      file,
      key,
      bucket,
      metadata: {
        uploadedBy: session.user.id,
        uploadedAt: new Date().toISOString(),
        ...parsedMetadata,
      },
    });

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    // Log upload
    await createInfrastructureAuditLog(
      'storage.upload_object',
      'storage_object',
      key,
      { 
        bucket, 
        size: file.size, 
        contentType: file.type,
        metadata: parsedMetadata 
      },
      request
    );

    return NextResponse.json({
      success: true,
      object: result.data,
      message: 'Object uploaded successfully',
    });

  } catch (error) {
    console.error('Failed to upload storage object:', error);

    if (error instanceof InfrastructureAuthError) {
      return NextResponse.json(
        { error: error.message },
        { status: error.code === 'INSUFFICIENT_PERMISSIONS' ? 403 : 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to upload object' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication and permissions
    const session = await requireStorageAccess('delete');
    const { bucket } = params;

    // Get object key from request body
    const body = await request.json();
    const { key } = body;

    if (!key) {
      return NextResponse.json(
        { error: 'Object key is required' },
        { status: 400 }
      );
    }

    // Delete object
    const result = await storageService.deleteObject(bucket, key);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }

    // Log deletion
    await createInfrastructureAuditLog(
      'storage.delete_object',
      'storage_object',
      key,
      { bucket },
      request
    );

    return NextResponse.json({
      success: true,
      message: 'Object deleted successfully',
    });

  } catch (error) {
    console.error('Failed to delete storage object:', error);

    if (error instanceof InfrastructureAuthError) {
      return NextResponse.json(
        { error: error.message },
        { status: error.code === 'INSUFFICIENT_PERMISSIONS' ? 403 : 401 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete object' },
      { status: 500 }
    );
  }
}
