import NextAuth from "next-auth";
import authConfig from "@/auth.config";
import { checkInfrastructureHealth } from "@/lib/infrastructure/services";
import { config } from "@/lib/config/environment";

const { auth } = NextAuth(authConfig);

export default auth(async (req) => {
  const { nextUrl } = req;
  const isLoggedIn = !!req.auth;
  const userRole = req.auth?.user?.role;

  const isApiAuthRoute = nextUrl.pathname.startsWith("/api/auth");
  const isAuthRoute = nextUrl.pathname.startsWith("/auth");
  const isInfrastructureRoute = nextUrl.pathname.startsWith("/dashboard/infrastructure") ||
                               nextUrl.pathname.startsWith("/dashboard/storage") ||
                               nextUrl.pathname.startsWith("/dashboard/metrics") ||
                               nextUrl.pathname.startsWith("/dashboard/containers") ||
                               nextUrl.pathname.startsWith("/dashboard/workspaces");
  const isInfrastructureApiRoute = nextUrl.pathname.startsWith("/api/infrastructure");
  const isPublicRoute = nextUrl.pathname === "/" ||
                        nextUrl.pathname.startsWith("/blog") ||
                        nextUrl.pathname.startsWith("/pricing") ||
                        nextUrl.pathname.startsWith("/about") ||
                        nextUrl.pathname.startsWith("/docs") ||
                        nextUrl.hash === "#demo";

  // Allow auth routes
  if (isApiAuthRoute) {
    return;
  }

  // Handle auth page redirects
  if (isAuthRoute) {
    if (isLoggedIn) {
      return Response.redirect(new URL("/dashboard", nextUrl));
    }
    return;
  }

  // Require authentication for protected routes
  if (!isLoggedIn && !isPublicRoute) {
    return Response.redirect(new URL("/auth/login", nextUrl));
  }

  // Infrastructure access control
  if (isLoggedIn && (isInfrastructureRoute || isInfrastructureApiRoute)) {
    // Check if user has infrastructure access permissions
    const hasInfrastructureAccess = userRole === 'ADMIN' || userRole === 'SUPER_ADMIN';

    if (!hasInfrastructureAccess) {
      if (isInfrastructureApiRoute) {
        return new Response(
          JSON.stringify({ error: 'Insufficient permissions for infrastructure access' }),
          { status: 403, headers: { 'Content-Type': 'application/json' } }
        );
      }
      return Response.redirect(new URL("/dashboard?error=insufficient-permissions", nextUrl));
    }

    // Check infrastructure health for critical operations
    if (isInfrastructureApiRoute && req.method !== 'GET') {
      try {
        const health = await checkInfrastructureHealth();

        if (health.overall === 'unhealthy') {
          return new Response(
            JSON.stringify({
              error: 'Infrastructure services are currently unavailable',
              health: health.overall
            }),
            { status: 503, headers: { 'Content-Type': 'application/json' } }
          );
        }
      } catch (error) {
        console.error('Health check failed in middleware:', error);
        // Continue with request if health check fails to avoid blocking
      }
    }
  }

  // Rate limiting for infrastructure API routes
  if (isInfrastructureApiRoute && isLoggedIn) {
    const rateLimitKey = `${req.auth?.user?.id}-${nextUrl.pathname}`;
    const rateLimitResult = await checkRateLimit(rateLimitKey, req.method || 'GET');

    if (!rateLimitResult.allowed) {
      return new Response(
        JSON.stringify({
          error: 'Rate limit exceeded',
          retryAfter: rateLimitResult.retryAfter
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': rateLimitResult.retryAfter.toString()
          }
        }
      );
    }
  }

  return;
});

// Rate limiting implementation
async function checkRateLimit(key: string, method: string): Promise<{ allowed: boolean; retryAfter: number }> {
  // Simple in-memory rate limiting - in production, use Redis
  const rateLimits = new Map<string, { count: number; resetTime: number }>();

  const limits = {
    GET: config.rateLimit.requestsPerMinute,
    POST: Math.floor(config.rateLimit.requestsPerMinute / 2),
    PUT: Math.floor(config.rateLimit.requestsPerMinute / 2),
    DELETE: Math.floor(config.rateLimit.requestsPerMinute / 4),
  };

  const limit = limits[method as keyof typeof limits] || limits.GET;
  const windowMs = 60 * 1000; // 1 minute
  const now = Date.now();

  const current = rateLimits.get(key);

  if (!current || now > current.resetTime) {
    rateLimits.set(key, { count: 1, resetTime: now + windowMs });
    return { allowed: true, retryAfter: 0 };
  }

  if (current.count >= limit) {
    const retryAfter = Math.ceil((current.resetTime - now) / 1000);
    return { allowed: false, retryAfter };
  }

  current.count++;
  return { allowed: true, retryAfter: 0 };
}

// Optionally, don't invoke middleware on some paths
export const config = {
  matcher: ["/((?!.+\\.[\\w]+$|_next).*)", "/", "/(api|trpc)(.*)"],
};