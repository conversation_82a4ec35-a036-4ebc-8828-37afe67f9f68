/**
 * Docker Container Service
 * Production-ready service for Docker container management
 */

import { BaseInfrastructureService, retry, cache } from './base';
import { config } from '@/lib/config/environment';
import {
  Container,
  ContainerPort,
  ContainerVolume,
  ContainerStats,
  RestartPolicy,
  ApiResponse,
} from '../types';

export class DockerService extends BaseInfrastructureService {
  private readonly socketPath: string;
  private readonly networkName: string;

  constructor() {
    super('docker', config.docker.apiUrl || 'http://localhost:2376');
    
    this.socketPath = config.docker.socketPath;
    this.networkName = config.docker.networkName;

    if (config.docker.apiUrl) {
      this.validateConfig(['DOCKER_API_URL']);
    }
  }

  protected getAuthHeaders(): Record<string, string> {
    return {
      'Authorization': `Bearer ${config.security.infrastructureJwtSecret}`,
      'X-Admin-Key': config.security.adminApiKey,
    };
  }

  // =============================================================================
  // CONTAINER MANAGEMENT
  // =============================================================================

  @cache(5000) // Cache for 5 seconds
  async listContainers(all: boolean = false): Promise<ApiResponse<Container[]>> {
    try {
      const params = new URLSearchParams({ all: all.toString() });
      const response = await this.makeRequest<any[]>(`/containers/json?${params}`);
      
      if (!response.success) {
        return response as ApiResponse<Container[]>;
      }

      const containers: Container[] = response.data?.map(this.mapDockerContainer) || [];
      this.log('info', `Listed ${containers.length} containers`);
      
      return this.formatSuccess(containers);
    } catch (error) {
      return this.formatError(error);
    }
  }

  async getContainer(containerId: string): Promise<ApiResponse<Container>> {
    try {
      this.validateParams({ containerId }, ['containerId']);

      const response = await this.makeRequest<any>(`/containers/${containerId}/json`);
      
      if (!response.success) {
        return response as ApiResponse<Container>;
      }

      const container = this.mapDockerContainer(response.data!);
      this.log('info', `Retrieved container: ${containerId}`);
      
      return this.formatSuccess(container);
    } catch (error) {
      return this.formatError(error);
    }
  }

  @retry(3, 2000)
  async createContainer(request: CreateContainerRequest): Promise<ApiResponse<Container>> {
    try {
      this.validateParams(request, ['name', 'image']);

      const containerConfig = {
        Image: request.image,
        Cmd: request.command,
        Env: Object.entries(request.environment || {}).map(([key, value]) => `${key}=${value}`),
        ExposedPorts: this.formatExposedPorts(request.ports || []),
        HostConfig: {
          PortBindings: this.formatPortBindings(request.ports || []),
          Binds: this.formatBinds(request.volumes || []),
          RestartPolicy: { Name: request.restartPolicy || 'unless-stopped' },
          NetworkMode: request.networkMode || this.networkName,
        },
        Labels: {
          'vibekraft.managed': 'true',
          'vibekraft.created': new Date().toISOString(),
          ...request.labels,
        },
        NetworkingConfig: {
          EndpointsConfig: {
            [this.networkName]: {},
          },
        },
      };

      const response = await this.makeRequest<{ Id: string }>('/containers/create', {
        method: 'POST',
        body: containerConfig,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.success) {
        return response as ApiResponse<Container>;
      }

      const containerId = response.data!.Id;
      
      // Get the created container details
      const containerResponse = await this.getContainer(containerId);
      
      if (containerResponse.success) {
        this.log('info', `Created container: ${request.name} (${containerId})`);
      }

      return containerResponse;
    } catch (error) {
      return this.formatError(error);
    }
  }

  async startContainer(containerId: string): Promise<ApiResponse<void>> {
    try {
      this.validateParams({ containerId }, ['containerId']);

      const response = await this.makeRequest(`/containers/${containerId}/start`, {
        method: 'POST',
      });

      if (response.success) {
        this.log('info', `Started container: ${containerId}`);
      }

      return response;
    } catch (error) {
      return this.formatError(error);
    }
  }

  async stopContainer(containerId: string, timeout: number = 10): Promise<ApiResponse<void>> {
    try {
      this.validateParams({ containerId }, ['containerId']);

      const params = new URLSearchParams({ t: timeout.toString() });
      const response = await this.makeRequest(`/containers/${containerId}/stop?${params}`, {
        method: 'POST',
      });

      if (response.success) {
        this.log('info', `Stopped container: ${containerId}`);
      }

      return response;
    } catch (error) {
      return this.formatError(error);
    }
  }

  async restartContainer(containerId: string, timeout: number = 10): Promise<ApiResponse<void>> {
    try {
      this.validateParams({ containerId }, ['containerId']);

      const params = new URLSearchParams({ t: timeout.toString() });
      const response = await this.makeRequest(`/containers/${containerId}/restart?${params}`, {
        method: 'POST',
      });

      if (response.success) {
        this.log('info', `Restarted container: ${containerId}`);
      }

      return response;
    } catch (error) {
      return this.formatError(error);
    }
  }

  async deleteContainer(containerId: string, force: boolean = false): Promise<ApiResponse<void>> {
    try {
      this.validateParams({ containerId }, ['containerId']);

      const params = new URLSearchParams({ force: force.toString() });
      const response = await this.makeRequest(`/containers/${containerId}?${params}`, {
        method: 'DELETE',
      });

      if (response.success) {
        this.log('info', `Deleted container: ${containerId}`);
      }

      return response;
    } catch (error) {
      return this.formatError(error);
    }
  }

  // =============================================================================
  // CONTAINER STATS & MONITORING
  // =============================================================================

  async getContainerStats(containerId: string): Promise<ApiResponse<ContainerStats>> {
    try {
      this.validateParams({ containerId }, ['containerId']);

      const params = new URLSearchParams({ stream: 'false' });
      const response = await this.makeRequest<any>(`/containers/${containerId}/stats?${params}`);
      
      if (!response.success) {
        return response as ApiResponse<ContainerStats>;
      }

      const stats = this.mapContainerStats(response.data!);
      return this.formatSuccess(stats);
    } catch (error) {
      return this.formatError(error);
    }
  }

  async getContainerLogs(
    containerId: string,
    options: LogOptions = {}
  ): Promise<ApiResponse<string[]>> {
    try {
      this.validateParams({ containerId }, ['containerId']);

      const params = new URLSearchParams({
        stdout: 'true',
        stderr: 'true',
        tail: (options.tail || 100).toString(),
        timestamps: (options.timestamps || false).toString(),
      });

      if (options.since) {
        params.append('since', Math.floor(options.since.getTime() / 1000).toString());
      }

      const response = await this.makeRequest<string>(`/containers/${containerId}/logs?${params}`);
      
      if (!response.success) {
        return response as ApiResponse<string[]>;
      }

      // Parse Docker logs format
      const logs = this.parseDockerLogs(response.data || '');
      return this.formatSuccess(logs);
    } catch (error) {
      return this.formatError(error);
    }
  }

  // =============================================================================
  // IMAGE MANAGEMENT
  // =============================================================================

  @cache(30000) // Cache for 30 seconds
  async listImages(): Promise<ApiResponse<DockerImage[]>> {
    try {
      const response = await this.makeRequest<any[]>('/images/json');
      
      if (!response.success) {
        return response as ApiResponse<DockerImage[]>;
      }

      const images: DockerImage[] = response.data?.map(this.mapDockerImage) || [];
      this.log('info', `Listed ${images.length} images`);
      
      return this.formatSuccess(images);
    } catch (error) {
      return this.formatError(error);
    }
  }

  async pullImage(imageName: string, tag: string = 'latest'): Promise<ApiResponse<void>> {
    try {
      this.validateParams({ imageName }, ['imageName']);

      const params = new URLSearchParams({ fromImage: imageName, tag });
      const response = await this.makeRequest(`/images/create?${params}`, {
        method: 'POST',
        timeout: 300000, // 5 minutes for image pull
      });

      if (response.success) {
        this.log('info', `Pulled image: ${imageName}:${tag}`);
      }

      return response;
    } catch (error) {
      return this.formatError(error);
    }
  }

  async deleteImage(imageId: string, force: boolean = false): Promise<ApiResponse<void>> {
    try {
      this.validateParams({ imageId }, ['imageId']);

      const params = new URLSearchParams({ force: force.toString() });
      const response = await this.makeRequest(`/images/${imageId}?${params}`, {
        method: 'DELETE',
      });

      if (response.success) {
        this.log('info', `Deleted image: ${imageId}`);
      }

      return response;
    } catch (error) {
      return this.formatError(error);
    }
  }

  // =============================================================================
  // NETWORK MANAGEMENT
  // =============================================================================

  async listNetworks(): Promise<ApiResponse<DockerNetwork[]>> {
    try {
      const response = await this.makeRequest<any[]>('/networks');
      
      if (!response.success) {
        return response as ApiResponse<DockerNetwork[]>;
      }

      const networks: DockerNetwork[] = response.data?.map(this.mapDockerNetwork) || [];
      return this.formatSuccess(networks);
    } catch (error) {
      return this.formatError(error);
    }
  }

  // =============================================================================
  // PRIVATE METHODS
  // =============================================================================

  private mapDockerContainer(dockerContainer: any): Container {
    return {
      id: dockerContainer.Id,
      name: dockerContainer.Names?.[0]?.replace('/', '') || dockerContainer.Id.substring(0, 12),
      image: dockerContainer.Image,
      command: dockerContainer.Command ? dockerContainer.Command.split(' ') : [],
      status: this.mapContainerStatus(dockerContainer.State || dockerContainer.Status),
      createdAt: new Date(dockerContainer.Created * 1000),
      updatedAt: new Date(),
      ports: this.mapContainerPorts(dockerContainer.Ports || []),
      volumes: this.mapContainerVolumes(dockerContainer.Mounts || []),
      environment: {},
      restartPolicy: 'unless-stopped',
      networkMode: dockerContainer.HostConfig?.NetworkMode || this.networkName,
      labels: dockerContainer.Labels || {},
    };
  }

  private mapContainerStatus(state: string): Container['status'] {
    const statusMap: Record<string, Container['status']> = {
      'running': 'running',
      'exited': 'stopped',
      'created': 'creating',
      'restarting': 'running',
      'removing': 'deleting',
      'paused': 'stopped',
      'dead': 'error',
    };

    return statusMap[state.toLowerCase()] || 'unknown';
  }

  private mapContainerPorts(ports: any[]): ContainerPort[] {
    return ports.map(port => ({
      containerPort: port.PrivatePort,
      hostPort: port.PublicPort,
      protocol: port.Type as 'tcp' | 'udp',
    }));
  }

  private mapContainerVolumes(mounts: any[]): ContainerVolume[] {
    return mounts.map(mount => ({
      hostPath: mount.Source,
      containerPath: mount.Destination,
      readOnly: mount.RW === false,
    }));
  }

  private mapContainerStats(stats: any): ContainerStats {
    const cpuDelta = stats.cpu_stats.cpu_usage.total_usage - stats.precpu_stats.cpu_usage.total_usage;
    const systemDelta = stats.cpu_stats.system_cpu_usage - stats.precpu_stats.system_cpu_usage;
    const cpuPercent = (cpuDelta / systemDelta) * stats.cpu_stats.online_cpus * 100;

    return {
      cpuPercent: Math.round(cpuPercent * 100) / 100,
      memoryUsage: stats.memory_stats.usage,
      memoryLimit: stats.memory_stats.limit,
      networkRx: stats.networks?.eth0?.rx_bytes || 0,
      networkTx: stats.networks?.eth0?.tx_bytes || 0,
      blockRead: stats.blkio_stats.io_service_bytes_recursive?.[0]?.value || 0,
      blockWrite: stats.blkio_stats.io_service_bytes_recursive?.[1]?.value || 0,
    };
  }

  private mapDockerImage(dockerImage: any): DockerImage {
    return {
      id: dockerImage.Id,
      tags: dockerImage.RepoTags || [],
      size: dockerImage.Size,
      created: new Date(dockerImage.Created * 1000),
    };
  }

  private mapDockerNetwork(dockerNetwork: any): DockerNetwork {
    return {
      id: dockerNetwork.Id,
      name: dockerNetwork.Name,
      driver: dockerNetwork.Driver,
      scope: dockerNetwork.Scope,
      created: new Date(dockerNetwork.Created),
    };
  }

  private formatExposedPorts(ports: ContainerPort[]): Record<string, {}> {
    const exposedPorts: Record<string, {}> = {};
    ports.forEach(port => {
      exposedPorts[`${port.containerPort}/${port.protocol}`] = {};
    });
    return exposedPorts;
  }

  private formatPortBindings(ports: ContainerPort[]): Record<string, any[]> {
    const portBindings: Record<string, any[]> = {};
    ports.forEach(port => {
      if (port.hostPort) {
        portBindings[`${port.containerPort}/${port.protocol}`] = [
          { HostPort: port.hostPort.toString() }
        ];
      }
    });
    return portBindings;
  }

  private formatBinds(volumes: ContainerVolume[]): string[] {
    return volumes.map(volume => 
      `${volume.hostPath}:${volume.containerPath}${volume.readOnly ? ':ro' : ''}`
    );
  }

  private parseDockerLogs(logData: string): string[] {
    // Docker logs format includes headers, strip them
    return logData.split('\n')
      .map(line => line.substring(8)) // Remove Docker log header
      .filter(line => line.trim().length > 0);
  }
}

// =============================================================================
// TYPES
// =============================================================================

export interface CreateContainerRequest {
  name: string;
  image: string;
  command?: string[];
  environment?: Record<string, string>;
  ports?: ContainerPort[];
  volumes?: ContainerVolume[];
  restartPolicy?: RestartPolicy;
  networkMode?: string;
  labels?: Record<string, string>;
}

export interface LogOptions {
  tail?: number;
  since?: Date;
  timestamps?: boolean;
}

export interface DockerImage {
  id: string;
  tags: string[];
  size: number;
  created: Date;
}

export interface DockerNetwork {
  id: string;
  name: string;
  driver: string;
  scope: string;
  created: Date;
}

// =============================================================================
// SINGLETON INSTANCE
// =============================================================================

export const dockerService = new DockerService();
